services:
  - type: web
    name: cyberdefensesim
    runtime: node
    plan: free
    buildCommand: npm ci && npm run build
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 5000
      - key: ALLOWED_ORIGINS
        value: https://cyberdefensesim.onrender.com
      - key: DATABASE_URL
        fromDatabase:
          name: cyberdefensesim-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: SESSION_SECRET
        generateValue: true
    domains:
      - cyberdefensesim.onrender.com

databases:
  - name: cyberdefensesim-db
    databaseName: cyberdefense_sim
    user: cyberdefense
    plan: free
