{"name": "recharts", "version": "2.15.2", "description": "React charts", "main": "lib/index.js", "module": "es6/index.js", "jsnext:main": "es6/index.js", "types": "types/index.d.ts", "sideEffects": false, "files": ["*.md", "es6", "lib", "umd", "types"], "keywords": ["react", "reactjs", "chart", "react-component"], "scripts": {"prepare": "husky install", "build": "npm run build-types && npm run build-cjs && npm run build-es6 && npm run build-umd", "build-cjs": "rimraf lib && cross-env NODE_ENV=commonjs babel ./src -d lib --extensions '.js,.ts,.tsx'", "build-es6": "rimraf es6 && cross-env NODE_ENV=es6 babel ./src -d es6 --extensions '.js,.ts,.tsx'", "build-umd": "rimraf umd && cross-env NODE_ENV=production webpack --entry ./src/index.ts -o umd", "build-types": "rimraf types && npm run tsc", "demo": "webpack serve --config demo/webpack.config.js --port 3000 --host 127.0.0.1 --progress --profile --static demo/", "test": "vitest run --config vitest.config.ts", "test-coverage": "vitest run --config vitest.config.ts --coverage", "test-watch": "vitest --config vitest.config.ts", "lint": "eslint \"./src/**/*.{ts,tsx}\"", "lint-test": "eslint \"./test/**/*.{ts,tsx}\"", "lint-storybook": "eslint \"./storybook/**/*.{ts,tsx}\"", "autofix": "eslint \"./src/**/*.{ts,tsx}\" --fix", "analyse": "cross-env NODE_ENV=analyse webpack ./src/index.ts -o umd/Recharts.js", "tsc": "tsc", "storybook": "npx storybook dev -p 6006 -c storybook", "build-storybook": "npx storybook build -o storybook/public -c storybook", "chromatic": "npx chromatic", "test-storybook": "test-storybook --config-dir storybook", "test-storybook:url": "test-storybook --url http://127.0.0.1:9009 --config-dir storybook", "test-storybook:ci": "concurrently -k -s first -n \"SB,TEST\" -c \"magenta,blue\" \"npm run build-storybook --quiet && npx http-server storybook/public --port 9009 --silent\" \"wait-on tcp:127.0.0.1:9009 && npm run test-storybook:url --maxWorkers=2\""}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/recharts/recharts.git"}, "author": {"name": "recharts group"}, "bugs": {"url": "https://github.com/recharts/recharts/issues"}, "homepage": "https://github.com/recharts/recharts", "peerDependencies": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "dependencies": {"clsx": "^2.0.0", "eventemitter3": "^4.0.1", "lodash": "^4.17.21", "react-is": "^18.3.1", "react-smooth": "^4.0.4", "recharts-scale": "^0.4.4", "tiny-invariant": "^1.3.1", "victory-vendor": "^36.6.8"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.2", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.23.2", "@babel/runtime": "^7.23.2", "@storybook/addon-a11y": "^8.1.2", "@storybook/addon-docs": "^8.1.2", "@storybook/addon-essentials": "^8.1.2", "@storybook/addon-interactions": "^8.1.2", "@storybook/addon-links": "^8.1.2", "@storybook/addon-mdx-gfm": "^8.1.2", "@storybook/addon-storysource": "^8.1.2", "@storybook/addon-webpack5-compiler-swc": "^1.0.2", "@storybook/manager-api": "^8.1.2", "@storybook/react": "^8.1.2", "@storybook/react-webpack5": "^8.1.2", "@storybook/source-loader": "^8.1.2", "@storybook/test-runner": "^0.18.0", "@storybook/theming": "^8.1.2", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@types/d3-interpolate": "^3.0.1", "@types/d3-shape": "^3.1.0", "@types/d3-time-format": "^4.0.0", "@types/lodash": "^4.14.144", "@types/node": "^14.18.34", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-is": "^18.3.0", "@types/react-router-dom": "^5.1.7", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vitejs/plugin-react": "^4.0.4", "@vitest/coverage-v8": "^0.34.6", "babel-loader": "^9.1.3", "babel-plugin-dev-expression": "^0.2.3", "babel-plugin-lodash": "^3.3.4", "browserslist": "^4.21.0", "chromatic": "^11.4.0", "concurrently": "^7.6.0", "cross-env": "^7.0.3", "d3-scale-chromatic": "^3.0.0", "d3-time": "^3.1.0", "d3-time-format": "^4.1.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.15", "husky": "^8.0.2", "jsdom": "^22.1.0", "lint-staged": "^13.2.2", "prettier": "^2.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^5.1.2", "rimraf": "^3.0.2", "storybook": "^8.1.2", "terser-webpack-plugin": "^5.1.1", "ts-loader": "^8.0.12", "typescript": "4.9.5", "update-browserslist-db": "^1.0.10", "vitest": "^0.34.5", "vitest-axe": "^0.1.0", "webpack": "^5.65.0", "webpack-bundle-analyzer": "^4.4.0", "webpack-cli": "^4.9.0", "webpack-dev-server": "^4.6.0"}, "engines": {"node": ">=14"}, "license": "MIT"}