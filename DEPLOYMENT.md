# Deployment Guide

## Render.com Deployment

### Quick Fix for CORS Issues

If you're experiencing CORS errors in production, ensure these environment variables are set in your Render dashboard:

1. **ALLOWED_ORIGINS**: `https://cyberdefensesim.onrender.com`
2. **NODE_ENV**: `production`

### Environment Variables Required

Set these in your Render service environment variables:

```
NODE_ENV=production
PORT=5000
ALLOWED_ORIGINS=https://cyberdefensesim.onrender.com
DATABASE_URL=<your-postgres-connection-string>
JWT_SECRET=<generate-a-secure-32-character-secret>
SESSION_SECRET=<generate-a-secure-32-character-secret>
```

### Automatic Deployment with render.yaml

This project includes a `render.yaml` file for automatic deployment. To use it:

1. Connect your GitHub repository to Render
2. Render will automatically detect the `render.yaml` file
3. It will create both the web service and PostgreSQL database
4. Environment variables will be configured automatically

### Manual Deployment Steps

If not using the render.yaml file:

1. **Create a new Web Service** on Render
2. **Connect your GitHub repository**
3. **Configure build settings**:
   - Build Command: `npm ci && npm run build`
   - Start Command: `npm start`
4. **Set environment variables** (see list above)
5. **Create a PostgreSQL database** and link it to your service

### CORS Configuration

The application now automatically handles CORS for:
- Development: `localhost:3000`, `localhost:5000`
- Production: Render domains (auto-detected)
- Custom domains: Set via `ALLOWED_ORIGINS` environment variable

### Troubleshooting CORS Issues

1. **Check logs** for CORS-related warnings
2. **Verify environment variables** are set correctly
3. **Ensure domain matches** exactly (including https://)
4. **Check browser network tab** for actual request origins

### Health Check

The application provides a health check endpoint at `/health` that Render uses to verify the service is running correctly.

### Static Assets

Static assets (JS, CSS) are now served with appropriate CORS headers in production to prevent cross-origin issues.
