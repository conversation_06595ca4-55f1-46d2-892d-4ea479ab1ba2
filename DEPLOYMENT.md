# Deployment Guide

## 🚨 URGENT: Fixing Current Deployment Issues

### Step 1: Generate Environment Variables

Run this command to generate secure environment variables:

```bash
npm run generate:env
```

This will output the required environment variables with secure random values.

### Step 2: Set Environment Variables in Render

1. **Go to your Render dashboard**
2. **Navigate to your CyberDefenseSim service**
3. **Click on "Environment" in the left sidebar**
4. **Add these environment variables:**

```
NODE_ENV=production
PORT=5000
ALLOWED_ORIGINS=https://cyberdefensesim.onrender.com
JWT_SECRET=<copy-from-generate:env-output>
SESSION_SECRET=<copy-from-generate:env-output>
```

### Step 3: Database Setup

**Option A: Using render.yaml (Recommended)**
- The `render.yaml` file will automatically create a PostgreSQL database
- DATABASE_URL will be set automatically
- Just push the render.yaml file to your repository

**Option B: Manual Database Setup**
1. Create a new PostgreSQL database in Render
2. Copy the connection string
3. Add it as DATABASE_URL environment variable

### Step 4: Deploy

After setting environment variables:
1. **Save the environment variables**
2. **Trigger a new deployment** (or it will auto-deploy)
3. **Check the logs** for successful startup

## Render.com Deployment

### Quick Fix for CORS Issues

The CORS issues have been fixed in the code. Once the environment variables are set correctly, the CORS errors should be resolved.

### Environment Variables Required

**CRITICAL**: These must be set for the application to start:

### Automatic Deployment with render.yaml

This project includes a `render.yaml` file for automatic deployment. To use it:

1. Connect your GitHub repository to Render
2. Render will automatically detect the `render.yaml` file
3. It will create both the web service and PostgreSQL database
4. Environment variables will be configured automatically

### Manual Deployment Steps

If not using the render.yaml file:

1. **Create a new Web Service** on Render
2. **Connect your GitHub repository**
3. **Configure build settings**:
   - Build Command: `npm ci && npm run build`
   - Start Command: `npm start`
4. **Set environment variables** (see list above)
5. **Create a PostgreSQL database** and link it to your service

### CORS Configuration

The application now automatically handles CORS for:
- Development: `localhost:3000`, `localhost:5000`
- Production: Render domains (auto-detected)
- Custom domains: Set via `ALLOWED_ORIGINS` environment variable

### Troubleshooting CORS Issues

1. **Check logs** for CORS-related warnings
2. **Verify environment variables** are set correctly
3. **Ensure domain matches** exactly (including https://)
4. **Check browser network tab** for actual request origins

### Health Check

The application provides a health check endpoint at `/health` that Render uses to verify the service is running correctly.

### Static Assets

Static assets (JS, CSS) are now served with appropriate CORS headers in production to prevent cross-origin issues.

## 🔧 Troubleshooting Deployment Issues

### Issue: "Missing required environment variables for production"

**Solution:**
1. Run `npm run generate:env` to get secure values
2. Set JWT_SECRET and SESSION_SECRET in Render dashboard
3. Redeploy the service

### Issue: "Failed to initialize database client" or "Invalid URL"

**Possible Causes & Solutions:**

1. **DATABASE_URL not set:**
   - Use render.yaml for automatic database creation
   - Or manually create PostgreSQL database in Render

2. **Invalid DATABASE_URL format:**
   - Must start with `postgresql://` or `postgres://`
   - Check the connection string format

3. **Database not accessible:**
   - Ensure database is in the same region as your service
   - Check database status in Render dashboard

### Issue: Application exits with status 1

**Check these in order:**

1. **Environment Variables:**
   ```bash
   # Required for production:
   NODE_ENV=production
   JWT_SECRET=<32+ character random string>
   SESSION_SECRET=<32+ character random string>
   ```

2. **Database Connection:**
   - Verify DATABASE_URL is set correctly
   - Check database is running and accessible

3. **Build Process:**
   - Ensure `npm run build` completes successfully
   - Check for TypeScript compilation errors

### Issue: CORS errors after deployment

**Verification Steps:**

1. **Check logs for CORS configuration:**
   - Look for "CORS configuration initialized" log entry
   - Verify allowed origins include your domain

2. **Test endpoints:**
   - Visit `https://your-app.onrender.com/health`
   - Should return JSON response without CORS errors

3. **Browser console:**
   - Check for JavaScript/CSS loading errors
   - CORS errors should be resolved

### Getting Help

If issues persist:

1. **Check Render logs:**
   - Go to your service dashboard
   - Click "Logs" to see detailed error messages

2. **Test locally:**
   - Run `npm run test:cors` to verify CORS configuration
   - Ensure app starts locally with production environment

3. **Environment validation:**
   - Use `npm run generate:env` to verify all required variables
