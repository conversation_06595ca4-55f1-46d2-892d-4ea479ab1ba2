# 🚀 Render Deployment Checklist

## Pre-Deployment Setup

### ✅ Step 1: Generate Environment Variables
```bash
npm run generate:env
```
Copy the output values for use in Render dashboard.

### ✅ Step 2: Render Dashboard Configuration

**Go to your Render service → Environment Variables and add:**

| Variable | Value | Notes |
|----------|-------|-------|
| `NODE_ENV` | `production` | Required |
| `PORT` | `5000` | Required |
| `JWT_SECRET` | `<from generate:env>` | 32+ character random string |
| `SESSION_SECRET` | `<from generate:env>` | 32+ character random string |
| `ALLOWED_ORIGINS` | `https://cyberdefensesim.onrender.com` | Your domain |

**Database Setup (Choose One):**

**Option A: Automatic (render.yaml)**
- Push `render.yaml` to your repository
- Render will create PostgreSQL database automatically
- `DATABASE_URL` will be set automatically

**Option B: Manual**
- Create PostgreSQL database in Render dashboard
- Copy connection string to `DATABASE_URL` environment variable

### ✅ Step 3: Deploy

1. **Save environment variables** in Render dashboard
2. **Trigger deployment** (manual or push to main branch)
3. **Monitor deployment logs** for any errors

## Post-Deployment Verification

### ✅ Step 4: Check Deployment Health
```bash
npm run check:deployment
```

This will verify:
- ✅ Health endpoint responds correctly
- ✅ CORS configuration is working
- ✅ Static assets are served properly

### ✅ Step 5: Manual Verification

**Test these URLs in your browser:**

1. **Health Check:** `https://cyberdefensesim.onrender.com/health`
   - Should return JSON with success: true

2. **Application:** `https://cyberdefensesim.onrender.com/`
   - Should load without CORS errors
   - Check browser console for JavaScript/CSS loading

3. **API Endpoints:** `https://cyberdefensesim.onrender.com/api/`
   - Should return appropriate responses

## Troubleshooting

### ❌ If deployment fails with "Missing required environment variables"

1. Double-check JWT_SECRET and SESSION_SECRET are set
2. Ensure values are 32+ characters long
3. Verify NODE_ENV is set to "production"

### ❌ If deployment fails with database errors

1. Check DATABASE_URL format: `postgresql://user:pass@host:port/dbname`
2. Verify database is running and accessible
3. Check database region matches service region

### ❌ If CORS errors persist

1. Verify ALLOWED_ORIGINS includes your exact domain
2. Check browser console for specific error messages
3. Run `npm run test:cors` locally to test configuration

### ❌ If static assets don't load

1. Check build completed successfully
2. Verify dist/public directory exists
3. Check Content-Type headers in browser network tab

## Success Criteria

Your deployment is successful when:

- ✅ Service starts without errors (exit code 0)
- ✅ Health check returns 200 OK
- ✅ Application loads in browser without CORS errors
- ✅ JavaScript and CSS files load correctly
- ✅ API endpoints respond appropriately

## Quick Commands Reference

```bash
# Generate secure environment variables
npm run generate:env

# Test CORS configuration locally
npm run test:cors

# Check deployment health
npm run check:deployment

# Build for production
npm run build

# Start production server locally
npm start
```

## Support

If you encounter issues:

1. Check Render service logs for detailed error messages
2. Review the DEPLOYMENT.md file for detailed troubleshooting
3. Ensure all environment variables are correctly set
4. Verify database connectivity and configuration
