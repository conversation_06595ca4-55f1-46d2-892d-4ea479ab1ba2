name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'

jobs:
  # Code Quality and Security Checks
  quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run Prettier check
        run: npm run format:check

      - name: Run TypeScript check
        run: npm run type-check

      - name: Security audit
        run: npm audit --audit-level=moderate

      - name: Check for vulnerabilities
        run: |
          npx audit-ci --moderate

  # Unit and Integration Tests
  test:
    name: Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: cyberdefense_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup test environment
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/cyberdefense_test
          JWT_SECRET: test-jwt-secret-key-for-testing-only
          SESSION_SECRET: test-session-secret-key-for-testing
          NODE_ENV: test
        run: |
          npm run db:push

      - name: Run unit tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/cyberdefense_test
          JWT_SECRET: test-jwt-secret-key-for-testing-only
          SESSION_SECRET: test-session-secret-key-for-testing
          NODE_ENV: test
        run: npm run test

      - name: Run tests with coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/cyberdefense_test
          JWT_SECRET: test-jwt-secret-key-for-testing-only
          SESSION_SECRET: test-session-secret-key-for-testing
          NODE_ENV: test
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Build and Package
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [quality, test]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            dist/
            package.json
            package-lock.json
          retention-days: 7

  # End-to-End Tests
  e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [build]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: cyberdefense_e2e
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Install dependencies
        run: npm ci

      - name: Setup E2E environment
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/cyberdefense_e2e
          JWT_SECRET: test-jwt-secret-key-for-e2e-testing
          SESSION_SECRET: test-session-secret-key-for-e2e
          NODE_ENV: test
          PORT: 5000
        run: |
          npm run db:push
          npm start &
          sleep 10

      - name: Run E2E tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/cyberdefense_e2e
          JWT_SECRET: test-jwt-secret-key-for-e2e-testing
          SESSION_SECRET: test-session-secret-key-for-e2e
          NODE_ENV: test
          PORT: 5000
        run: |
          # Add E2E test command when implemented
          echo "E2E tests would run here"

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [build]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, e2e, security]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment"
          # Add actual deployment commands here

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, e2e, security]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts

      - name: Deploy to production
        run: |
          echo "Deploying to production environment"
          # Add actual deployment commands here

  # Notify on completion
  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
      - name: Notify deployment status
        run: |
          echo "Pipeline completed. Notification would be sent here."
          # Add notification logic (Slack, email, etc.)
