version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cyberdefense-db
    environment:
      POSTGRES_DB: cyberdefense_sim
      POSTGRES_USER: cyberdefense
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_change_in_production}
      POSTGRES_INITDB_ARGS: '--auth-host=scram-sha-256'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    ports:
      - '5432:5432'
    networks:
      - cyberdefense-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U cyberdefense -d cyberdefense_sim']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: cyberdefense-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password_change_in_production}
    volumes:
      - redis_data:/data
    ports:
      - '6379:6379'
    networks:
      - cyberdefense-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5

  # Main Application
  app:
    build:
      context: .
      target: production
    container_name: cyberdefense-app
    environment:
      NODE_ENV: production
      PORT: 5000
      DATABASE_URL: postgresql://cyberdefense:${DB_PASSWORD:-secure_password_change_in_production}@postgres:5432/cyberdefense_sim
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_password_change_in_production}@redis:6379
      JWT_SECRET: ${JWT_SECRET:-change_this_jwt_secret_in_production}
      SESSION_SECRET: ${SESSION_SECRET:-change_this_session_secret_in_production}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
    ports:
      - '5000:5000'
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cyberdefense-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5000/health']
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - app_logs:/app/logs

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: cyberdefense-nginx
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - cyberdefense-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  cyberdefense-network:
    driver: bridge

# Development override
---
version: '3.8'

# docker-compose.override.yml for development
services:
  app:
    build:
      target: development
    environment:
      NODE_ENV: development
      DATABASE_URL: ****************************************************/cyberdefense_sim
      REDIS_URL: redis://:dev_password@redis:6379
      JWT_SECRET: dev_jwt_secret_not_for_production
      SESSION_SECRET: dev_session_secret_not_for_production
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
    ports:
      - '5000:5000'
      - '9229:9229' # Debug port

  postgres:
    environment:
      POSTGRES_PASSWORD: dev_password

  redis:
    command: redis-server --requirepass dev_password

  # Development tools
  adminer:
    image: adminer
    container_name: cyberdefense-adminer
    ports:
      - '8080:8080'
    networks:
      - cyberdefense-network
    depends_on:
      - postgres

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: cyberdefense-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379:0:dev_password
    ports:
      - '8081:8081'
    networks:
      - cyberdefense-network
    depends_on:
      - redis
