var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// server/index.ts
import { createServer } from "http";

// server/app.ts
import express from "express";
import cors from "cors";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import path2 from "path";

// server/logger.ts
import winston from "winston";
var isDevelopment = process.env.NODE_ENV === "development";
var developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: "HH:mm:ss" }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp: timestamp2, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : "";
    return `${timestamp2} [${level}]: ${message} ${metaStr}`;
  })
);
var productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);
var logger = winston.createLogger({
  level: isDevelopment ? "debug" : "info",
  format: isDevelopment ? developmentFormat : productionFormat,
  defaultMeta: { service: "cyberdefense-sim" },
  transports: [
    new winston.transports.Console({
      silent: process.env.NODE_ENV === "test"
    })
  ]
});
if (!isDevelopment) {
  logger.add(
    new winston.transports.File({
      filename: "logs/error.log",
      level: "error",
      maxsize: 5242880,
      // 5MB
      maxFiles: 5
    })
  );
  logger.add(
    new winston.transports.File({
      filename: "logs/combined.log",
      maxsize: 5242880,
      // 5MB
      maxFiles: 5
    })
  );
}
if (!isDevelopment) {
  import("fs").then((fs2) => {
    if (!fs2.existsSync("logs")) {
      fs2.mkdirSync("logs");
    }
  });
}

// server/error-handler.ts
import { ZodError } from "zod";
var CustomError = class extends Error {
  statusCode;
  isOperational;
  code;
  constructor(message, statusCode = 500, isOperational = true, code) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;
    Error.captureStackTrace(this, this.constructor);
  }
};
var ValidationError = class extends CustomError {
  constructor(message, details) {
    super(message, 400, true, "VALIDATION_ERROR");
    this.name = "ValidationError";
  }
};
var NotFoundError = class extends CustomError {
  constructor(message = "Resource not found") {
    super(message, 404, true, "NOT_FOUND_ERROR");
    this.name = "NotFoundError";
  }
};
var ConflictError = class extends CustomError {
  constructor(message = "Resource conflict") {
    super(message, 409, true, "CONFLICT_ERROR");
    this.name = "ConflictError";
  }
};
var DatabaseError = class extends CustomError {
  constructor(message = "Database operation failed") {
    super(message, 500, true, "DATABASE_ERROR");
    this.name = "DatabaseError";
  }
};
function formatErrorResponse(error, req) {
  const isDevelopment2 = process.env.NODE_ENV === "development";
  const baseResponse = {
    success: false,
    message: error.message,
    code: error.code,
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    path: req.path,
    method: req.method
  };
  if (isDevelopment2) {
    return {
      ...baseResponse,
      stack: error.stack,
      details: error
    };
  }
  return baseResponse;
}
function handleZodError(error) {
  const messages = error.errors.map((err) => {
    const path3 = err.path.join(".");
    return `${path3}: ${err.message}`;
  });
  return new ValidationError(`Validation failed: ${messages.join(", ")}`);
}
function handleDatabaseError(error) {
  switch (error.code) {
    case "23505":
      return new ConflictError("Resource already exists");
    case "23503":
      return new ValidationError("Referenced resource does not exist");
    case "23502":
      return new ValidationError("Required field is missing");
    case "42P01":
      return new DatabaseError("Database table not found");
    default:
      return new DatabaseError("Database operation failed");
  }
}
var errorHandler = (error, req, res, next) => {
  let appError = error;
  if (error instanceof ZodError) {
    appError = handleZodError(error);
  } else if (error.name === "DatabaseError" || error.code) {
    appError = handleDatabaseError(error);
  } else if (!appError.statusCode) {
    appError = new CustomError(
      process.env.NODE_ENV === "production" ? "Internal server error" : error.message,
      500,
      false
    );
  }
  const logLevel = appError.statusCode >= 500 ? "error" : "warn";
  logger[logLevel]("Request error", {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      statusCode: appError.statusCode,
      code: appError.code
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get("User-Agent")
    },
    user: req.user?.id
  });
  const response = formatErrorResponse(appError, req);
  res.status(appError.statusCode || 500).json(response);
};
var notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.method} ${req.path} not found`);
  next(error);
};
var setupGlobalErrorHandlers = () => {
  process.on("unhandledRejection", (reason, promise) => {
    logger.error("Unhandled Rejection", {
      reason: reason?.message || reason,
      stack: reason?.stack,
      promise: promise.toString()
    });
    if (process.env.NODE_ENV === "production") {
      process.exit(1);
    }
  });
  process.on("uncaughtException", (error) => {
    logger.error("Uncaught Exception", {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  });
};

// server/auth.ts
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";

// server/database-storage.ts
import { eq, and, desc } from "drizzle-orm";

// server/db.ts
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";

// shared/schema.ts
var schema_exports = {};
__export(schema_exports, {
  achievements: () => achievements,
  domains: () => domains,
  insertAchievementSchema: () => insertAchievementSchema,
  insertDomainSchema: () => insertDomainSchema,
  insertRefreshTokenSchema: () => insertRefreshTokenSchema,
  insertScenarioSchema: () => insertScenarioSchema,
  insertUserAchievementSchema: () => insertUserAchievementSchema,
  insertUserProgressSchema: () => insertUserProgressSchema,
  insertUserScenarioSchema: () => insertUserScenarioSchema,
  insertUserSchema: () => insertUserSchema,
  refreshTokens: () => refreshTokens,
  scenarios: () => scenarios,
  sessions: () => sessions,
  userAchievements: () => userAchievements,
  userProgress: () => userProgress,
  userScenarios: () => userScenarios,
  users: () => users
});
import { pgTable, text, serial, integer, boolean, jsonb, timestamp, varchar, index } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
var sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull()
  },
  (table) => [index("IDX_session_expire").on(table.expire)]
);
var users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique().notNull(),
  firstName: varchar("first_name").notNull(),
  lastName: varchar("last_name").notNull(),
  passwordHash: varchar("password_hash"),
  // For JWT auth users
  profileImageUrl: varchar("profile_image_url"),
  xp: integer("xp").default(0).notNull(),
  streak: integer("streak").default(0).notNull(),
  lastActivity: timestamp("last_activity"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var refreshTokens = pgTable(
  "refresh_tokens",
  {
    id: serial("id").primaryKey(),
    userId: varchar("user_id").notNull(),
    token: varchar("token").notNull(),
    expiresAt: timestamp("expires_at").notNull(),
    createdAt: timestamp("created_at").defaultNow()
  },
  (table) => [index("IDX_refresh_tokens_user_id").on(table.userId), index("IDX_refresh_tokens_token").on(table.token)]
);
var domains = pgTable("domains", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  examPercentage: integer("exam_percentage").notNull(),
  color: text("color").notNull(),
  icon: text("icon").notNull()
});
var scenarios = pgTable("scenarios", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  type: text("type").notNull(),
  // 'lab', 'scenario', 'challenge'
  domainId: integer("domain_id").notNull(),
  difficulty: text("difficulty").notNull(),
  // 'beginner', 'intermediate', 'advanced'
  estimatedTime: integer("estimated_time").notNull(),
  // in minutes
  xpReward: integer("xp_reward").notNull(),
  content: jsonb("content").notNull()
  // scenario content and questions
});
var userProgress = pgTable("user_progress", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull(),
  // Changed to varchar for Replit auth
  domainId: integer("domain_id").notNull(),
  progress: integer("progress").default(0).notNull(),
  // percentage 0-100
  questionsCompleted: integer("questions_completed").default(0).notNull(),
  questionsCorrect: integer("questions_correct").default(0).notNull(),
  timeSpent: integer("time_spent").default(0).notNull()
  // in minutes
});
var userScenarios = pgTable("user_scenarios", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull(),
  // Changed to varchar for Replit auth
  scenarioId: integer("scenario_id").notNull(),
  completed: boolean("completed").default(false).notNull(),
  score: integer("score"),
  // percentage 0-100
  attempts: integer("attempts").default(0).notNull(),
  timeSpent: integer("time_spent").default(0).notNull(),
  completedAt: timestamp("completed_at")
});
var achievements = pgTable("achievements", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  icon: text("icon").notNull(),
  xpReward: integer("xp_reward").notNull(),
  criteria: jsonb("criteria").notNull()
  // conditions for earning the achievement
});
var userAchievements = pgTable("user_achievements", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull(),
  // Changed to varchar for Replit auth
  achievementId: integer("achievement_id").notNull(),
  earnedAt: timestamp("earned_at").defaultNow().notNull()
});
var insertUserSchema = createInsertSchema(users);
var insertDomainSchema = createInsertSchema(domains).omit({
  id: true
});
var insertScenarioSchema = createInsertSchema(scenarios).omit({
  id: true
});
var insertUserProgressSchema = createInsertSchema(userProgress).omit({
  id: true
});
var insertUserScenarioSchema = createInsertSchema(userScenarios).omit({
  id: true
});
var insertAchievementSchema = createInsertSchema(achievements).omit({
  id: true
});
var insertUserAchievementSchema = createInsertSchema(userAchievements).omit({
  id: true
});
var insertRefreshTokenSchema = createInsertSchema(refreshTokens).omit({
  id: true,
  createdAt: true
});

// server/db.ts
import fs from "fs";
import path from "path";
var connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  logger.warn("DATABASE_URL not set. Database features will be disabled.");
}
var client = null;
var db = null;
if (connectionString) {
  try {
    client = postgres(connectionString, {
      max: 10,
      idle_timeout: 20,
      connect_timeout: 10
    });
    db = drizzle(client, { schema: schema_exports });
    logger.info("Database client initialized");
  } catch (error) {
    logger.error("Failed to initialize database client", { error: error instanceof Error ? error.message : String(error) });
  }
}
async function runMigrations() {
  try {
    logger.info("Starting database migrations...");
    const migrationsDir = path.join(process.cwd(), "migrations");
    if (!fs.existsSync(migrationsDir)) {
      logger.warn("Migrations directory not found, skipping migrations");
      return;
    }
    const migrationFiles = fs.readdirSync(migrationsDir).filter((file) => file.endsWith(".sql")).sort();
    for (const file of migrationFiles) {
      const filePath = path.join(migrationsDir, file);
      const sql = fs.readFileSync(filePath, "utf8");
      logger.info(`Running migration: ${file}`);
      const statements = sql.split(";").map((stmt) => stmt.trim()).filter((stmt) => stmt.length > 0);
      for (const statement of statements) {
        if (statement.trim()) {
          await client.unsafe(statement);
        }
      }
      logger.info(`Completed migration: ${file}`);
    }
    logger.info("All migrations completed successfully");
  } catch (error) {
    logger.error("Migration failed", { error: error instanceof Error ? error.message : String(error) });
    throw error;
  }
}
async function checkDatabaseConnection() {
  if (!client) {
    logger.warn("Database client not available");
    return false;
  }
  try {
    await client`SELECT 1`;
    logger.info("Database connection successful");
    return true;
  } catch (error) {
    logger.error("Database connection failed", { error: error instanceof Error ? error.message : String(error) });
    return false;
  }
}
async function closeDatabaseConnection() {
  if (!client) {
    return;
  }
  try {
    await client.end();
    logger.info("Database connection closed");
  } catch (error) {
    logger.error("Error closing database connection", { error: error instanceof Error ? error.message : String(error) });
  }
}
async function initializeDatabase() {
  if (!connectionString) {
    logger.warn("Skipping database initialization - no DATABASE_URL provided");
    return;
  }
  try {
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      logger.warn("Database connection failed, continuing without database");
      return;
    }
    await runMigrations();
    logger.info("Database initialization completed");
  } catch (error) {
    logger.error("Database initialization failed", { error: error instanceof Error ? error.message : String(error) });
    logger.warn("Continuing without database functionality");
  }
}

// server/database-storage.ts
var DatabaseStorage = class {
  constructor() {
    if (!db) {
      throw new Error("Database not available");
    }
  }
  // User operations
  async getUser(id) {
    if (!db) throw new Error("Database not available");
    try {
      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
      return result[0];
    } catch (error) {
      logger.error("Error getting user", { error: error instanceof Error ? error.message : String(error), userId: id });
      throw error;
    }
  }
  async getUserByEmail(email) {
    try {
      const result = await db.select().from(users).where(eq(users.email, email.toLowerCase())).limit(1);
      return result[0];
    } catch (error) {
      logger.error("Error getting user by email", { error: error.message, email });
      throw error;
    }
  }
  async upsertUser(userData) {
    try {
      const userToInsert = {
        id: userData.id,
        email: userData.email?.toLowerCase() || "",
        firstName: userData.firstName || "",
        lastName: userData.lastName || "",
        passwordHash: userData.passwordHash || null,
        profileImageUrl: userData.profileImageUrl || null,
        xp: userData.xp || 0,
        streak: userData.streak || 0,
        lastActivity: userData.lastActivity || /* @__PURE__ */ new Date(),
        createdAt: userData.createdAt || /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      const result = await db.insert(users).values(userToInsert).onConflictDoUpdate({
        target: users.id,
        set: {
          email: userToInsert.email,
          firstName: userToInsert.firstName,
          lastName: userToInsert.lastName,
          passwordHash: userToInsert.passwordHash,
          profileImageUrl: userToInsert.profileImageUrl,
          xp: userToInsert.xp,
          streak: userToInsert.streak,
          lastActivity: userToInsert.lastActivity,
          updatedAt: userToInsert.updatedAt
        }
      }).returning();
      return result[0];
    } catch (error) {
      logger.error("Error upserting user", { error: error.message, userData });
      throw error;
    }
  }
  async updateUserXP(userId, xp) {
    try {
      await db.update(users).set({ xp, updatedAt: /* @__PURE__ */ new Date() }).where(eq(users.id, userId));
    } catch (error) {
      logger.error("Error updating user XP", { error: error.message, userId, xp });
      throw error;
    }
  }
  async updateUserStreak(userId, streak) {
    try {
      await db.update(users).set({ streak, updatedAt: /* @__PURE__ */ new Date() }).where(eq(users.id, userId));
    } catch (error) {
      logger.error("Error updating user streak", { error: error.message, userId, streak });
      throw error;
    }
  }
  async updateUserActivity(userId) {
    try {
      await db.update(users).set({ lastActivity: /* @__PURE__ */ new Date(), updatedAt: /* @__PURE__ */ new Date() }).where(eq(users.id, userId));
    } catch (error) {
      logger.error("Error updating user activity", { error: error.message, userId });
      throw error;
    }
  }
  // Refresh token operations
  async storeRefreshToken(userId, token) {
    try {
      const expiresAt = /* @__PURE__ */ new Date();
      expiresAt.setDate(expiresAt.getDate() + 30);
      await db.insert(refreshTokens).values({
        userId,
        token,
        expiresAt
      });
    } catch (error) {
      logger.error("Error storing refresh token", { error: error.message, userId });
      throw error;
    }
  }
  async getRefreshToken(token) {
    try {
      const result = await db.select().from(refreshTokens).where(eq(refreshTokens.token, token)).limit(1);
      return result[0];
    } catch (error) {
      logger.error("Error getting refresh token", { error: error.message });
      throw error;
    }
  }
  async deleteRefreshToken(token) {
    try {
      await db.delete(refreshTokens).where(eq(refreshTokens.token, token));
    } catch (error) {
      logger.error("Error deleting refresh token", { error: error.message });
      throw error;
    }
  }
  async deleteUserRefreshTokens(userId) {
    try {
      await db.delete(refreshTokens).where(eq(refreshTokens.userId, userId));
    } catch (error) {
      logger.error("Error deleting user refresh tokens", { error: error.message, userId });
      throw error;
    }
  }
  // Domain operations
  async getAllDomains() {
    try {
      return await db.select().from(domains).orderBy(domains.id);
    } catch (error) {
      logger.error("Error getting all domains", { error: error.message });
      throw error;
    }
  }
  async getDomain(id) {
    try {
      const result = await db.select().from(domains).where(eq(domains.id, id)).limit(1);
      return result[0];
    } catch (error) {
      logger.error("Error getting domain", { error: error.message, domainId: id });
      throw error;
    }
  }
  // Scenario operations
  async getAllScenarios() {
    try {
      return await db.select().from(scenarios).orderBy(scenarios.id);
    } catch (error) {
      logger.error("Error getting all scenarios", { error: error.message });
      throw error;
    }
  }
  async getScenariosByDomain(domainId) {
    try {
      return await db.select().from(scenarios).where(eq(scenarios.domainId, domainId)).orderBy(scenarios.id);
    } catch (error) {
      logger.error("Error getting scenarios by domain", { error: error.message, domainId });
      throw error;
    }
  }
  async getScenario(id) {
    try {
      const result = await db.select().from(scenarios).where(eq(scenarios.id, id)).limit(1);
      return result[0];
    } catch (error) {
      logger.error("Error getting scenario", { error: error.message, scenarioId: id });
      throw error;
    }
  }
  // User progress operations
  async getUserProgress(userId) {
    try {
      return await db.select().from(userProgress).where(eq(userProgress.userId, userId));
    } catch (error) {
      logger.error("Error getting user progress", { error: error.message, userId });
      throw error;
    }
  }
  async getUserProgressByDomain(userId, domainId) {
    try {
      const result = await db.select().from(userProgress).where(and(eq(userProgress.userId, userId), eq(userProgress.domainId, domainId))).limit(1);
      return result[0];
    } catch (error) {
      logger.error("Error getting user progress by domain", { error: error.message, userId, domainId });
      throw error;
    }
  }
  async updateUserProgress(userId, domainId, progress) {
    try {
      const existing = await this.getUserProgressByDomain(userId, domainId);
      if (existing) {
        await db.update(userProgress).set(progress).where(and(eq(userProgress.userId, userId), eq(userProgress.domainId, domainId)));
      } else {
        await db.insert(userProgress).values({
          userId,
          domainId,
          progress: progress.progress || 0,
          questionsCompleted: progress.questionsCompleted || 0,
          questionsCorrect: progress.questionsCorrect || 0,
          timeSpent: progress.timeSpent || 0
        });
      }
    } catch (error) {
      logger.error("Error updating user progress", { error: error.message, userId, domainId, progress });
      throw error;
    }
  }
  // User scenario operations
  async getUserScenarios(userId) {
    try {
      return await db.select().from(userScenarios).where(eq(userScenarios.userId, userId));
    } catch (error) {
      logger.error("Error getting user scenarios", { error: error.message, userId });
      throw error;
    }
  }
  async getUserScenario(userId, scenarioId) {
    try {
      const result = await db.select().from(userScenarios).where(and(eq(userScenarios.userId, userId), eq(userScenarios.scenarioId, scenarioId))).limit(1);
      return result[0];
    } catch (error) {
      logger.error("Error getting user scenario", { error: error.message, userId, scenarioId });
      throw error;
    }
  }
  async updateUserScenario(userId, scenarioId, data) {
    try {
      const existing = await this.getUserScenario(userId, scenarioId);
      if (existing) {
        await db.update(userScenarios).set(data).where(and(eq(userScenarios.userId, userId), eq(userScenarios.scenarioId, scenarioId)));
      } else {
        await db.insert(userScenarios).values({
          userId,
          scenarioId,
          completed: data.completed || false,
          score: data.score || null,
          attempts: data.attempts || 0,
          timeSpent: data.timeSpent || 0,
          completedAt: data.completedAt || null
        });
      }
    } catch (error) {
      logger.error("Error updating user scenario", { error: error.message, userId, scenarioId, data });
      throw error;
    }
  }
  // Achievement operations
  async getAllAchievements() {
    try {
      return await db.select().from(achievements).orderBy(achievements.id);
    } catch (error) {
      logger.error("Error getting all achievements", { error: error.message });
      throw error;
    }
  }
  async getUserAchievements(userId) {
    try {
      return await db.select().from(userAchievements).where(eq(userAchievements.userId, userId)).orderBy(desc(userAchievements.earnedAt));
    } catch (error) {
      logger.error("Error getting user achievements", { error: error.message, userId });
      throw error;
    }
  }
  async awardAchievement(userId, achievementId) {
    try {
      const existing = await db.select().from(userAchievements).where(and(eq(userAchievements.userId, userId), eq(userAchievements.achievementId, achievementId))).limit(1);
      if (existing.length === 0) {
        await db.insert(userAchievements).values({
          userId,
          achievementId,
          earnedAt: /* @__PURE__ */ new Date()
        });
        const achievement = await db.select().from(achievements).where(eq(achievements.id, achievementId)).limit(1);
        if (achievement[0]) {
          const user = await this.getUser(userId);
          if (user) {
            await this.updateUserXP(userId, user.xp + achievement[0].xpReward);
          }
        }
      }
    } catch (error) {
      logger.error("Error awarding achievement", { error: error.message, userId, achievementId });
      throw error;
    }
  }
};

// server/achievement-service.ts
var AchievementService = class {
  constructor(storage2) {
    this.storage = storage2;
  }
  async checkAndAwardAchievements(userId, context) {
    try {
      const awardedAchievements = [];
      const achievements2 = await this.storage.getAllAchievements();
      const userAchievements2 = await this.storage.getUserAchievements(userId);
      const earnedAchievementIds = new Set(userAchievements2.map((ua) => ua.achievementId));
      for (const achievement of achievements2) {
        if (earnedAchievementIds.has(achievement.id)) {
          continue;
        }
        const criteria = achievement.criteria;
        const shouldAward = await this.checkAchievementCriteria(userId, criteria, context);
        if (shouldAward) {
          await this.storage.awardAchievement(userId, achievement.id);
          awardedAchievements.push(achievement.id);
          logger.info("Achievement awarded", {
            userId,
            achievementId: achievement.id,
            achievementName: achievement.name
          });
        }
      }
      return awardedAchievements;
    } catch (error) {
      logger.error("Error checking achievements", { error: error.message, userId });
      return [];
    }
  }
  async checkAchievementCriteria(userId, criteria, context) {
    try {
      if (criteria.scenariosCompleted !== void 0) {
        const userScenarios2 = await this.storage.getUserScenarios(userId);
        const completedCount = userScenarios2.filter((us) => us.completed).length;
        if (completedCount < criteria.scenariosCompleted) {
          return false;
        }
      }
      if (criteria.domainProgress !== void 0) {
        const userProgress2 = await this.storage.getUserProgress(userId);
        const hasRequiredProgress = userProgress2.some((up) => up.progress >= criteria.domainProgress);
        if (!hasRequiredProgress) {
          return false;
        }
      }
      if (criteria.perfectScore && context?.score !== 100) {
        return false;
      }
      if (criteria.fastCompletion !== void 0 && context?.timeSpent) {
        if (context.timeSpent > criteria.fastCompletion) {
          return false;
        }
      }
      if (criteria.streak !== void 0) {
        const user = await this.storage.getUser(userId);
        if (!user || user.streak < criteria.streak) {
          return false;
        }
      }
      if (criteria.totalXP !== void 0) {
        const user = await this.storage.getUser(userId);
        if (!user || user.xp < criteria.totalXP) {
          return false;
        }
      }
      if (criteria.categoryComplete) {
        const isComplete = await this.checkCategoryCompletion(userId, criteria.categoryComplete);
        if (!isComplete) {
          return false;
        }
      }
      return true;
    } catch (error) {
      logger.error("Error checking achievement criteria", { error: error.message, userId, criteria });
      return false;
    }
  }
  async checkCategoryCompletion(userId, category) {
    try {
      const scenarios2 = await this.storage.getAllScenarios();
      const userScenarios2 = await this.storage.getUserScenarios(userId);
      const userCompletedIds = new Set(userScenarios2.filter((us) => us.completed).map((us) => us.scenarioId));
      let categoryScenarios = [];
      switch (category) {
        case "vulnerability":
          categoryScenarios = scenarios2.filter(
            (s) => s.title.toLowerCase().includes("vulnerability") || s.description.toLowerCase().includes("vulnerability")
          ).map((s) => s.id);
          break;
        case "incident-response":
          categoryScenarios = scenarios2.filter((s) => s.title.toLowerCase().includes("incident") || s.description.toLowerCase().includes("incident")).map((s) => s.id);
          break;
        default:
          return false;
      }
      return categoryScenarios.length > 0 && categoryScenarios.every((id) => userCompletedIds.has(id));
    } catch (error) {
      logger.error("Error checking category completion", { error: error.message, userId, category });
      return false;
    }
  }
  async updateUserStreak(userId) {
    try {
      const user = await this.storage.getUser(userId);
      if (!user) return;
      const now = /* @__PURE__ */ new Date();
      const lastActivity = user.lastActivity ? new Date(user.lastActivity) : null;
      if (!lastActivity) {
        await this.storage.updateUserStreak(userId, 1);
        return;
      }
      const daysDiff = Math.floor((now.getTime() - lastActivity.getTime()) / (1e3 * 60 * 60 * 24));
      if (daysDiff === 1) {
        await this.storage.updateUserStreak(userId, user.streak + 1);
      } else if (daysDiff > 1) {
        await this.storage.updateUserStreak(userId, 1);
      }
      await this.storage.updateUserActivity(userId);
      await this.checkAndAwardAchievements(userId);
    } catch (error) {
      logger.error("Error updating user streak", { error: error.message, userId });
    }
  }
  async calculateDomainProgress(userId, domainId) {
    try {
      const scenarios2 = await this.storage.getScenariosByDomain(domainId);
      if (scenarios2.length === 0) return 0;
      const userScenarios2 = await this.storage.getUserScenarios(userId);
      const completedInDomain = userScenarios2.filter(
        (us) => us.completed && scenarios2.some((s) => s.id === us.scenarioId)
      ).length;
      return Math.round(completedInDomain / scenarios2.length * 100);
    } catch (error) {
      logger.error("Error calculating domain progress", { error: error.message, userId, domainId });
      return 0;
    }
  }
  async updateDomainProgress(userId, domainId) {
    try {
      const progress = await this.calculateDomainProgress(userId, domainId);
      const existingProgress = await this.storage.getUserProgressByDomain(userId, domainId);
      const updateData = {
        progress,
        questionsCompleted: existingProgress?.questionsCompleted || 0,
        questionsCorrect: existingProgress?.questionsCorrect || 0,
        timeSpent: existingProgress?.timeSpent || 0
      };
      await this.storage.updateUserProgress(userId, domainId, updateData);
      await this.checkAndAwardAchievements(userId, {
        domainProgress: { domainId, progress }
      });
    } catch (error) {
      logger.error("Error updating domain progress", { error: error.message, userId, domainId });
    }
  }
  async getAchievementStats(userId) {
    try {
      const allAchievements = await this.storage.getAllAchievements();
      const userAchievements2 = await this.storage.getUserAchievements(userId);
      const earnedAchievementIds = new Set(userAchievements2.map((ua) => ua.achievementId));
      const earnedAchievementDetails = allAchievements.filter((a) => earnedAchievementIds.has(a.id));
      const totalXPFromAchievements = earnedAchievementDetails.reduce((sum, a) => sum + a.xpReward, 0);
      const recentAchievements = userAchievements2.slice(0, 5).map((ua) => {
        const achievement = allAchievements.find((a) => a.id === ua.achievementId);
        return {
          ...ua,
          name: achievement?.name,
          description: achievement?.description,
          icon: achievement?.icon,
          xpReward: achievement?.xpReward
        };
      });
      return {
        totalAchievements: allAchievements.length,
        earnedAchievements: userAchievements2.length,
        totalXPFromAchievements,
        recentAchievements
      };
    } catch (error) {
      logger.error("Error getting achievement stats", { error: error.message, userId });
      return {
        totalAchievements: 0,
        earnedAchievements: 0,
        totalXPFromAchievements: 0,
        recentAchievements: []
      };
    }
  }
};

// server/storage.ts
var SimpleStorage = class {
  users = /* @__PURE__ */ new Map();
  usersByEmail = /* @__PURE__ */ new Map();
  domains = /* @__PURE__ */ new Map();
  scenarios = /* @__PURE__ */ new Map();
  userProgress = /* @__PURE__ */ new Map();
  userScenarios = /* @__PURE__ */ new Map();
  achievements = /* @__PURE__ */ new Map();
  userAchievements = /* @__PURE__ */ new Map();
  refreshTokens = /* @__PURE__ */ new Map();
  constructor() {
    this.seedData();
  }
  seedData() {
    const domainsData = [
      {
        id: 1,
        name: "Threats, Attacks and Vulnerabilities",
        description: "Security threats and vulnerability management",
        examPercentage: 24,
        color: "#EF4444",
        icon: "Shield"
      },
      {
        id: 2,
        name: "Architecture and Design",
        description: "Secure architecture principles and design",
        examPercentage: 21,
        color: "#F59E0B",
        icon: "Building"
      },
      {
        id: 3,
        name: "Implementation",
        description: "Security implementation and configuration",
        examPercentage: 25,
        color: "#10B981",
        icon: "Settings"
      },
      {
        id: 4,
        name: "Operations and Incident Response",
        description: "Security operations and incident management",
        examPercentage: 16,
        color: "#3B82F6",
        icon: "AlertTriangle"
      },
      {
        id: 5,
        name: "Governance, Risk and Compliance",
        description: "Risk management and compliance frameworks",
        examPercentage: 14,
        color: "#8B5CF6",
        icon: "FileText"
      }
    ];
    domainsData.forEach((domain) => {
      this.domains.set(domain.id, domain);
    });
    const scenariosData = [
      {
        id: 1,
        title: "Network Vulnerability Assessment",
        description: "Conduct a comprehensive vulnerability scan using Nessus",
        type: "lab",
        domainId: 1,
        difficulty: "intermediate",
        estimatedTime: 45,
        xpReward: 150,
        content: {
          background: "Your organization needs a security assessment",
          scenario: "Perform vulnerability scanning and analysis",
          codeExample: "nmap -sV -sC target_network"
        }
      },
      {
        id: 2,
        title: "Incident Response Planning",
        description: "Develop and implement incident response procedures",
        type: "scenario",
        domainId: 4,
        difficulty: "advanced",
        estimatedTime: 60,
        xpReward: 200,
        content: {
          background: "Security incident requires immediate response",
          scenario: "Follow incident response framework",
          codeExample: "Containment -> Eradication -> Recovery -> Lessons Learned"
        }
      },
      {
        id: 3,
        title: "Cryptography Implementation",
        description: "Implement secure encryption protocols",
        type: "lab",
        domainId: 3,
        difficulty: "expert",
        estimatedTime: 90,
        xpReward: 250,
        content: {
          background: "Data protection requires encryption",
          scenario: "Design cryptographic solution",
          codeExample: "AES-256 with proper key management"
        }
      }
    ];
    scenariosData.forEach((scenario) => {
      this.scenarios.set(scenario.id, scenario);
    });
    const achievementsData = [
      {
        id: 1,
        name: "First Steps",
        description: "Complete your first scenario",
        icon: "Trophy",
        xpReward: 50,
        criteria: { scenariosCompleted: 1 }
      },
      {
        id: 2,
        name: "Security Expert",
        description: "Complete 10 scenarios",
        icon: "Star",
        xpReward: 200,
        criteria: { scenariosCompleted: 10 }
      }
    ];
    achievementsData.forEach((achievement) => {
      this.achievements.set(achievement.id, achievement);
    });
  }
  async getUser(id) {
    return this.users.get(id);
  }
  async getUserByEmail(email) {
    return this.usersByEmail.get(email.toLowerCase());
  }
  async upsertUser(userData) {
    const user = {
      id: userData.id,
      email: userData.email || null,
      firstName: userData.firstName || null,
      lastName: userData.lastName || null,
      passwordHash: userData.passwordHash || null,
      profileImageUrl: userData.profileImageUrl || null,
      xp: userData.xp || 0,
      streak: userData.streak || 0,
      lastActivity: userData.lastActivity || /* @__PURE__ */ new Date(),
      createdAt: userData.createdAt || /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    this.users.set(user.id, user);
    if (user.email) {
      this.usersByEmail.set(user.email.toLowerCase(), user);
    }
    return user;
  }
  async updateUserXP(userId, xp) {
    const user = this.users.get(userId);
    if (user) {
      user.xp = xp;
      user.updatedAt = /* @__PURE__ */ new Date();
    }
  }
  async updateUserStreak(userId, streak) {
    const user = this.users.get(userId);
    if (user) {
      user.streak = streak;
      user.updatedAt = /* @__PURE__ */ new Date();
    }
  }
  async updateUserActivity(userId) {
    const user = this.users.get(userId);
    if (user) {
      user.lastActivity = /* @__PURE__ */ new Date();
      user.updatedAt = /* @__PURE__ */ new Date();
    }
  }
  // Refresh token operations
  async storeRefreshToken(userId, token) {
    const expiresAt = /* @__PURE__ */ new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);
    const refreshToken = {
      id: Date.now(),
      userId,
      token,
      expiresAt,
      createdAt: /* @__PURE__ */ new Date()
    };
    this.refreshTokens.set(token, refreshToken);
  }
  async getRefreshToken(token) {
    return this.refreshTokens.get(token);
  }
  async deleteRefreshToken(token) {
    this.refreshTokens.delete(token);
  }
  async deleteUserRefreshTokens(userId) {
    for (const [token, refreshToken] of this.refreshTokens.entries()) {
      if (refreshToken.userId === userId) {
        this.refreshTokens.delete(token);
      }
    }
  }
  async getAllDomains() {
    return Array.from(this.domains.values());
  }
  async getDomain(id) {
    return this.domains.get(id);
  }
  async getAllScenarios() {
    return Array.from(this.scenarios.values());
  }
  async getScenariosByDomain(domainId) {
    return Array.from(this.scenarios.values()).filter((s) => s.domainId === domainId);
  }
  async getScenario(id) {
    return this.scenarios.get(id);
  }
  async getUserProgress(userId) {
    return Array.from(this.userProgress.values()).filter((p) => p.userId === userId);
  }
  async getUserProgressByDomain(userId, domainId) {
    const key = `${userId}-${domainId}`;
    return this.userProgress.get(key);
  }
  async updateUserProgress(userId, domainId, progress) {
    const key = `${userId}-${domainId}`;
    const existing = this.userProgress.get(key);
    if (existing) {
      Object.assign(existing, progress);
    } else {
      const newProgress = {
        id: Date.now(),
        userId,
        domainId,
        progress: progress.progress || 0,
        questionsCompleted: progress.questionsCompleted || 0,
        questionsCorrect: progress.questionsCorrect || 0,
        timeSpent: progress.timeSpent || 0
      };
      this.userProgress.set(key, newProgress);
    }
  }
  async getUserScenarios(userId) {
    return Array.from(this.userScenarios.values()).filter((s) => s.userId === userId);
  }
  async getUserScenario(userId, scenarioId) {
    const key = `${userId}-${scenarioId}`;
    return this.userScenarios.get(key);
  }
  async updateUserScenario(userId, scenarioId, data) {
    const key = `${userId}-${scenarioId}`;
    const existing = this.userScenarios.get(key);
    if (existing) {
      Object.assign(existing, data);
    } else {
      const newUserScenario = {
        id: Date.now(),
        userId,
        scenarioId,
        completed: data.completed || false,
        score: data.score || null,
        attempts: data.attempts || 0,
        timeSpent: data.timeSpent || 0,
        completedAt: data.completedAt || null
      };
      this.userScenarios.set(key, newUserScenario);
    }
  }
  async getAllAchievements() {
    return Array.from(this.achievements.values());
  }
  async getUserAchievements(userId) {
    return Array.from(this.userAchievements.values()).filter((a) => a.userId === userId);
  }
  async awardAchievement(userId, achievementId) {
    const key = `${userId}-${achievementId}`;
    const userAchievement = {
      id: Date.now(),
      userId,
      achievementId,
      earnedAt: /* @__PURE__ */ new Date()
    };
    this.userAchievements.set(key, userAchievement);
  }
};
var storage;
try {
  if (db && (process.env.NODE_ENV === "production" || process.env.USE_DATABASE === "true")) {
    storage = new DatabaseStorage();
  } else {
    storage = new SimpleStorage();
  }
} catch (error) {
  console.warn("Failed to initialize database storage, falling back to simple storage:", error);
  storage = new SimpleStorage();
}
var achievementService = new AchievementService(storage);

// server/auth.ts
var JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-jwt-key-change-in-production";
var JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";
var REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || "30d";
var AuthService = class {
  static generateTokens(userId, email) {
    const accessToken = jwt.sign({ userId, email, type: "access" }, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN
    });
    const refreshToken = jwt.sign({ userId, email, type: "refresh" }, JWT_SECRET, {
      expiresIn: REFRESH_TOKEN_EXPIRES_IN
    });
    return { accessToken, refreshToken };
  }
  static verifyToken(token) {
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      return decoded;
    } catch (error) {
      logger.warn("Token verification failed", { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }
  static async hashPassword(password) {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }
  static async comparePassword(password, hashedPassword) {
    return bcrypt.compare(password, hashedPassword);
  }
  static extractTokenFromHeader(authHeader) {
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return null;
    }
    return authHeader.substring(7);
  }
};
var authenticateToken = async (req, res, next) => {
  try {
    const token = AuthService.extractTokenFromHeader(req.headers.authorization);
    if (!token) {
      res.status(401).json({
        success: false,
        message: "Access token required"
      });
      return;
    }
    const payload = AuthService.verifyToken(token);
    if (!payload || payload.type !== "access") {
      res.status(401).json({
        success: false,
        message: "Invalid or expired token"
      });
      return;
    }
    const user = await storage.getUser(payload.userId);
    if (!user) {
      res.status(401).json({
        success: false,
        message: "User not found"
      });
      return;
    }
    req.user = {
      id: user.id,
      email: user.email || "",
      firstName: user.firstName || "",
      lastName: user.lastName || ""
    };
    next();
  } catch (error) {
    logger.error("Authentication middleware error", { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      message: "Authentication error"
    });
  }
};
var optionalAuth = async (req, res, next) => {
  try {
    const token = AuthService.extractTokenFromHeader(req.headers.authorization);
    if (token) {
      const payload = AuthService.verifyToken(token);
      if (payload && payload.type === "access") {
        const user = await storage.getUser(payload.userId);
        if (user) {
          req.user = {
            id: user.id,
            email: user.email || "",
            firstName: user.firstName || "",
            lastName: user.lastName || ""
          };
        }
      }
    }
    next();
  } catch (error) {
    logger.warn("Optional auth middleware error", { error: error instanceof Error ? error.message : String(error) });
    next();
  }
};

// server/routes/auth.ts
import { Router } from "express";
import { nanoid } from "nanoid";

// server/validation.ts
import { z } from "zod";
var registerSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(50, "First name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/, "First name can only contain letters and spaces"),
  lastName: z.string().min(1, "Last name is required").max(50, "Last name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/, "Last name can only contain letters and spaces"),
  email: z.string().email("Invalid email format").max(255, "Email must be less than 255 characters"),
  password: z.string().min(8, "Password must be at least 8 characters").max(128, "Password must be less than 128 characters").regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
  )
});
var loginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(1, "Password is required")
});
var refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, "Refresh token is required")
});
var updateUserSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(50, "First name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/, "First name can only contain letters and spaces").optional(),
  lastName: z.string().min(1, "Last name is required").max(50, "Last name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/, "Last name can only contain letters and spaces").optional(),
  email: z.string().email("Invalid email format").max(255, "Email must be less than 255 characters").optional()
});
var updateProgressSchema = z.object({
  domainId: z.number().int().positive("Domain ID must be a positive integer"),
  progress: z.number().int().min(0).max(100, "Progress must be between 0 and 100"),
  questionsCompleted: z.number().int().min(0).optional(),
  questionsCorrect: z.number().int().min(0).optional(),
  timeSpent: z.number().int().min(0).optional()
});
var updateScenarioSchema = z.object({
  completed: z.boolean().optional(),
  score: z.number().int().min(0).max(100).optional(),
  attempts: z.number().int().min(0).optional(),
  timeSpent: z.number().int().min(0).optional()
});
var userIdParamSchema = z.object({
  id: z.string().min(1, "User ID is required")
});
var domainIdParamSchema = z.object({
  id: z.string().regex(/^\d+$/, "Domain ID must be a number").transform(Number)
});
var scenarioIdParamSchema = z.object({
  id: z.string().regex(/^\d+$/, "Scenario ID must be a number").transform(Number)
});
function validateBody(schema) {
  return (req, res, next) => {
    try {
      const result = schema.safeParse(req.body);
      if (!result.success) {
        const errors = result.error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message
        }));
        logger.warn("Request body validation failed", {
          url: req.url,
          method: req.method,
          errors
        });
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors
        });
      }
      req.body = result.data;
      next();
    } catch (error) {
      logger.error("Validation middleware error", { error: error.message });
      res.status(500).json({
        success: false,
        message: "Internal server error"
      });
    }
  };
}
function validateParams(schema) {
  return (req, res, next) => {
    try {
      const result = schema.safeParse(req.params);
      if (!result.success) {
        const errors = result.error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message
        }));
        logger.warn("Request params validation failed", {
          url: req.url,
          method: req.method,
          errors
        });
        return res.status(400).json({
          success: false,
          message: "Invalid parameters",
          errors
        });
      }
      req.params = result.data;
      next();
    } catch (error) {
      logger.error("Validation middleware error", { error: error.message });
      res.status(500).json({
        success: false,
        message: "Internal server error"
      });
    }
  };
}

// server/routes/auth.ts
var router = Router();
router.post("/register", validateBody(registerSchema), async (req, res) => {
  try {
    const { firstName, lastName, email, password } = req.body;
    const existingUser = await storage.getUserByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: "User with this email already exists"
      });
    }
    const passwordHash = await AuthService.hashPassword(password);
    const userId = nanoid();
    const user = await storage.upsertUser({
      id: userId,
      email,
      firstName,
      lastName,
      passwordHash,
      profileImageUrl: null
    });
    const { accessToken, refreshToken } = AuthService.generateTokens(userId, email);
    await storage.storeRefreshToken(userId, refreshToken);
    logger.info("User registered successfully", { userId, email });
    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          xp: user.xp,
          streak: user.streak
        },
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    logger.error("Registration error", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Registration failed"
    });
  }
});
router.post("/login", validateBody(loginSchema), async (req, res) => {
  try {
    const { email, password } = req.body;
    const user = await storage.getUserByEmail(email);
    if (!user || !user.passwordHash) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password"
      });
    }
    const isValidPassword = await AuthService.comparePassword(password, user.passwordHash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password"
      });
    }
    const { accessToken, refreshToken } = AuthService.generateTokens(user.id, user.email);
    await storage.storeRefreshToken(user.id, refreshToken);
    await storage.updateUserActivity(user.id);
    logger.info("User logged in successfully", { userId: user.id, email });
    res.json({
      success: true,
      message: "Login successful",
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          xp: user.xp,
          streak: user.streak
        },
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    logger.error("Login error", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Login failed"
    });
  }
});
router.post("/refresh", validateBody(refreshTokenSchema), async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const payload = AuthService.verifyToken(refreshToken);
    if (!payload || payload.type !== "refresh") {
      return res.status(401).json({
        success: false,
        message: "Invalid refresh token"
      });
    }
    const storedToken = await storage.getRefreshToken(refreshToken);
    if (!storedToken) {
      return res.status(401).json({
        success: false,
        message: "Refresh token not found"
      });
    }
    if (/* @__PURE__ */ new Date() > storedToken.expiresAt) {
      await storage.deleteRefreshToken(refreshToken);
      return res.status(401).json({
        success: false,
        message: "Refresh token expired"
      });
    }
    const user = await storage.getUser(payload.userId);
    if (!user) {
      await storage.deleteRefreshToken(refreshToken);
      return res.status(401).json({
        success: false,
        message: "User not found"
      });
    }
    const { accessToken, refreshToken: newRefreshToken } = AuthService.generateTokens(user.id, user.email);
    await storage.deleteRefreshToken(refreshToken);
    await storage.storeRefreshToken(user.id, newRefreshToken);
    logger.info("Token refreshed successfully", { userId: user.id });
    res.json({
      success: true,
      message: "Token refreshed successfully",
      data: {
        accessToken,
        refreshToken: newRefreshToken
      }
    });
  } catch (error) {
    logger.error("Token refresh error", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Token refresh failed"
    });
  }
});
router.post("/logout", validateBody(refreshTokenSchema), async (req, res) => {
  try {
    const { refreshToken } = req.body;
    await storage.deleteRefreshToken(refreshToken);
    logger.info("User logged out successfully");
    res.json({
      success: true,
      message: "Logout successful"
    });
  } catch (error) {
    logger.error("Logout error", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Logout failed"
    });
  }
});
router.get("/me", async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required"
      });
    }
    const user = await storage.getUser(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          xp: user.xp,
          streak: user.streak,
          lastActivity: user.lastActivity,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    logger.error("Get profile error", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to get user profile"
    });
  }
});
var auth_default = router;

// server/routes/users.ts
import { Router as Router2 } from "express";
var router2 = Router2();
router2.use(authenticateToken);
router2.get("/:id/dashboard", validateParams(userIdParamSchema), async (req, res) => {
  try {
    const userId = req.params.id;
    if (req.user?.id !== userId) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }
    const domains2 = await storage.getAllDomains();
    const userProgress2 = await storage.getUserProgress(userId);
    const scenarios2 = await storage.getAllScenarios();
    const achievements2 = await storage.getUserAchievements(userId);
    const domainsWithProgress = domains2.map((domain) => {
      const progress = userProgress2.find((p) => p.domainId === domain.id);
      return { ...domain, progress: progress?.progress || 0 };
    });
    const recommendedScenarios = scenarios2.slice(0, 3).map((scenario) => {
      const domain = domains2.find((d) => d.id === scenario.domainId);
      return { ...scenario, domainName: domain?.name || "Unknown" };
    });
    const overallProgress = Math.round(
      domainsWithProgress.reduce((sum, domain) => sum + domain.progress, 0) / domains2.length
    );
    const totalQuestionsCompleted = userProgress2.reduce((sum, p) => sum + (p.questionsCompleted || 0), 0);
    const totalQuestionsCorrect = userProgress2.reduce((sum, p) => sum + (p.questionsCorrect || 0), 0);
    const totalTimeSpent = userProgress2.reduce((sum, p) => sum + (p.timeSpent || 0), 0);
    const accuracy = totalQuestionsCompleted > 0 ? Math.round(totalQuestionsCorrect / totalQuestionsCompleted * 100) : 0;
    const weakestDomain = domainsWithProgress.reduce(
      (weakest, domain) => domain.progress < (weakest?.progress || 100) ? domain : weakest,
      null
    );
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          xp: user.xp,
          streak: user.streak
        },
        overallProgress,
        domains: domainsWithProgress,
        recentAchievements: achievements2.slice(0, 3),
        recommendedScenarios,
        stats: {
          accuracy,
          questionsCompleted: totalQuestionsCompleted,
          studyTime: totalTimeSpent,
          weakestDomain: weakestDomain?.id || null
        }
      }
    });
  } catch (error) {
    logger.error("Error fetching dashboard data", { error: error.message, userId: req.params.id });
    res.status(500).json({
      success: false,
      message: "Failed to fetch dashboard data"
    });
  }
});
router2.get("/:id", validateParams(userIdParamSchema), async (req, res) => {
  try {
    const userId = req.params.id;
    if (req.user?.id !== userId) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          xp: user.xp,
          streak: user.streak,
          lastActivity: user.lastActivity,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    logger.error("Error fetching user", { error: error.message, userId: req.params.id });
    res.status(500).json({
      success: false,
      message: "Failed to fetch user"
    });
  }
});
router2.put(
  "/:id",
  validateParams(userIdParamSchema),
  validateBody(updateUserSchema),
  async (req, res) => {
    try {
      const userId = req.params.id;
      if (req.user?.id !== userId) {
        return res.status(403).json({
          success: false,
          message: "Access denied"
        });
      }
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found"
        });
      }
      const updatedUser = await storage.upsertUser({
        ...user,
        ...req.body,
        id: userId,
        updatedAt: /* @__PURE__ */ new Date()
      });
      res.json({
        success: true,
        message: "Profile updated successfully",
        data: {
          user: {
            id: updatedUser.id,
            email: updatedUser.email,
            firstName: updatedUser.firstName,
            lastName: updatedUser.lastName,
            xp: updatedUser.xp,
            streak: updatedUser.streak
          }
        }
      });
    } catch (error) {
      logger.error("Error updating user", { error: error.message, userId: req.params.id });
      res.status(500).json({
        success: false,
        message: "Failed to update profile"
      });
    }
  }
);
router2.put(
  "/:id/progress/:domainId",
  validateParams(userIdParamSchema),
  validateParams(domainIdParamSchema),
  validateBody(updateProgressSchema),
  async (req, res) => {
    try {
      const userId = req.params.id;
      const domainId = parseInt(req.params.domainId);
      if (req.user?.id !== userId) {
        return res.status(403).json({
          success: false,
          message: "Access denied"
        });
      }
      await storage.updateUserProgress(userId, domainId, req.body);
      res.json({
        success: true,
        message: "Progress updated successfully"
      });
    } catch (error) {
      logger.error("Error updating user progress", { error: error.message, userId: req.params.id });
      res.status(500).json({
        success: false,
        message: "Failed to update progress"
      });
    }
  }
);
router2.put(
  "/:id/scenarios/:scenarioId",
  validateParams(userIdParamSchema),
  validateParams(scenarioIdParamSchema),
  validateBody(updateScenarioSchema),
  async (req, res) => {
    try {
      const userId = req.params.id;
      const scenarioId = parseInt(req.params.scenarioId);
      if (req.user?.id !== userId) {
        return res.status(403).json({
          success: false,
          message: "Access denied"
        });
      }
      await storage.updateUserScenario(userId, scenarioId, req.body);
      if (req.body.completed) {
        const scenario = await storage.getScenario(scenarioId);
        if (scenario) {
          const user = await storage.getUser(userId);
          if (user) {
            await storage.updateUserXP(userId, user.xp + scenario.xpReward);
            await achievementService.updateUserStreak(userId);
            await achievementService.updateDomainProgress(userId, scenario.domainId);
            const newAchievements = await achievementService.checkAndAwardAchievements(userId, {
              scenarioCompleted: true,
              scenarioId,
              score: req.body.score,
              timeSpent: req.body.timeSpent
            });
            return res.json({
              success: true,
              message: "Scenario progress updated successfully",
              data: {
                xpAwarded: scenario.xpReward,
                newAchievements: newAchievements.length
              }
            });
          }
        }
      }
      res.json({
        success: true,
        message: "Scenario progress updated successfully"
      });
    } catch (error) {
      logger.error("Error updating scenario progress", { error: error.message, userId: req.params.id });
      res.status(500).json({
        success: false,
        message: "Failed to update scenario progress"
      });
    }
  }
);
router2.get("/:id/achievements", validateParams(userIdParamSchema), async (req, res) => {
  try {
    const userId = req.params.id;
    if (req.user?.id !== userId) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }
    const stats = await achievementService.getAchievementStats(userId);
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error("Error fetching user achievements", { error: error.message, userId: req.params.id });
    res.status(500).json({
      success: false,
      message: "Failed to fetch achievements"
    });
  }
});
router2.get("/:id/progress", validateParams(userIdParamSchema), async (req, res) => {
  try {
    const userId = req.params.id;
    if (req.user?.id !== userId) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }
    const progress = await storage.getUserProgress(userId);
    const domains2 = await storage.getAllDomains();
    const totalDomains = domains2.length;
    const totalProgress = progress.reduce((sum, p) => sum + p.progress, 0);
    const overallProgress = totalDomains > 0 ? Math.round(totalProgress / totalDomains) : 0;
    res.json({
      success: true,
      data: {
        overallProgress,
        domains: progress,
        totalDomains
      }
    });
  } catch (error) {
    logger.error("Error fetching user progress", { error: error.message, userId: req.params.id });
    res.status(500).json({
      success: false,
      message: "Failed to fetch progress"
    });
  }
});
router2.get(
  "/:id/progress/:domainId",
  validateParams(userIdParamSchema),
  validateParams(domainIdParamSchema),
  async (req, res) => {
    try {
      const userId = req.params.id;
      const domainId = parseInt(req.params.domainId);
      if (req.user?.id !== userId) {
        return res.status(403).json({
          success: false,
          message: "Access denied"
        });
      }
      const progress = await storage.getUserProgressByDomain(userId, domainId);
      const domain = await storage.getDomain(domainId);
      if (!domain) {
        return res.status(404).json({
          success: false,
          message: "Domain not found"
        });
      }
      res.json({
        success: true,
        data: {
          domain,
          progress: progress || {
            progress: 0,
            questionsCompleted: 0,
            questionsCorrect: 0,
            timeSpent: 0
          }
        }
      });
    } catch (error) {
      logger.error("Error fetching domain progress", {
        error: error.message,
        userId: req.params.id,
        domainId: req.params.domainId
      });
      res.status(500).json({
        success: false,
        message: "Failed to fetch domain progress"
      });
    }
  }
);
var users_default = router2;

// server/routes/content.ts
import { Router as Router3 } from "express";
var router3 = Router3();
router3.use(optionalAuth);
router3.get("/domains", async (req, res) => {
  try {
    const domains2 = await storage.getAllDomains();
    if (req.user) {
      const userProgress2 = await storage.getUserProgress(req.user.id);
      const domainsWithProgress = domains2.map((domain) => {
        const progress = userProgress2.find((p) => p.domainId === domain.id);
        return { ...domain, progress: progress?.progress || 0 };
      });
      return res.json({
        success: true,
        data: { domains: domainsWithProgress }
      });
    }
    res.json({
      success: true,
      data: { domains: domains2 }
    });
  } catch (error) {
    logger.error("Error fetching domains", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch domains"
    });
  }
});
router3.get("/domains/:id", validateParams(domainIdParamSchema), async (req, res) => {
  try {
    const domainId = parseInt(req.params.id);
    const domain = await storage.getDomain(domainId);
    if (!domain) {
      return res.status(404).json({
        success: false,
        message: "Domain not found"
      });
    }
    const scenarios2 = await storage.getScenariosByDomain(domainId);
    let userProgress2 = null;
    let userScenarios2 = [];
    if (req.user) {
      const progress = await storage.getUserProgress(req.user.id);
      userProgress2 = progress.find((p) => p.domainId === domainId);
      userScenarios2 = await storage.getUserScenarios(req.user.id);
    }
    const scenariosWithProgress = scenarios2.map((scenario) => {
      const userScenario = userScenarios2.find((us) => us.scenarioId === scenario.id);
      return {
        ...scenario,
        completed: userScenario?.completed || false,
        score: userScenario?.score || null,
        attempts: userScenario?.attempts || 0
      };
    });
    res.json({
      success: true,
      data: {
        domain: {
          ...domain,
          progress: userProgress2?.progress || 0
        },
        scenarios: scenariosWithProgress
      }
    });
  } catch (error) {
    logger.error("Error fetching domain", { error: error.message, domainId: req.params.id });
    res.status(500).json({
      success: false,
      message: "Failed to fetch domain"
    });
  }
});
router3.get("/scenarios", async (req, res) => {
  try {
    const scenarios2 = await storage.getAllScenarios();
    const domains2 = await storage.getAllDomains();
    const scenariosWithDomains = scenarios2.map((scenario) => {
      const domain = domains2.find((d) => d.id === scenario.domainId);
      return { ...scenario, domainName: domain?.name || "Unknown" };
    });
    if (req.user) {
      const userScenarios2 = await storage.getUserScenarios(req.user.id);
      const scenariosWithProgress = scenariosWithDomains.map((scenario) => {
        const userScenario = userScenarios2.find((us) => us.scenarioId === scenario.id);
        return {
          ...scenario,
          completed: userScenario?.completed || false,
          score: userScenario?.score || null,
          attempts: userScenario?.attempts || 0
        };
      });
      return res.json({
        success: true,
        data: { scenarios: scenariosWithProgress }
      });
    }
    res.json({
      success: true,
      data: { scenarios: scenariosWithDomains }
    });
  } catch (error) {
    logger.error("Error fetching scenarios", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch scenarios"
    });
  }
});
router3.get(
  "/scenarios/:id",
  validateParams(scenarioIdParamSchema),
  async (req, res) => {
    try {
      const scenarioId = parseInt(req.params.id);
      const scenario = await storage.getScenario(scenarioId);
      if (!scenario) {
        return res.status(404).json({
          success: false,
          message: "Scenario not found"
        });
      }
      const domain = await storage.getDomain(scenario.domainId);
      let userScenario = null;
      if (req.user) {
        userScenario = await storage.getUserScenario(req.user.id, scenarioId);
      }
      res.json({
        success: true,
        data: {
          scenario: {
            ...scenario,
            domainName: domain?.name || "Unknown",
            completed: userScenario?.completed || false,
            score: userScenario?.score || null,
            attempts: userScenario?.attempts || 0,
            timeSpent: userScenario?.timeSpent || 0
          }
        }
      });
    } catch (error) {
      logger.error("Error fetching scenario", { error: error.message, scenarioId: req.params.id });
      res.status(500).json({
        success: false,
        message: "Failed to fetch scenario"
      });
    }
  }
);
router3.get("/achievements", async (req, res) => {
  try {
    const achievements2 = await storage.getAllAchievements();
    if (req.user) {
      const userAchievements2 = await storage.getUserAchievements(req.user.id);
      const achievementsWithProgress = achievements2.map((achievement) => {
        const userAchievement = userAchievements2.find((ua) => ua.achievementId === achievement.id);
        return {
          ...achievement,
          earned: !!userAchievement,
          earnedAt: userAchievement?.earnedAt || null
        };
      });
      return res.json({
        success: true,
        data: { achievements: achievementsWithProgress }
      });
    }
    res.json({
      success: true,
      data: { achievements: achievements2 }
    });
  } catch (error) {
    logger.error("Error fetching achievements", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch achievements"
    });
  }
});
var content_default = router3;

// server/routes/admin.ts
import { Router as Router4 } from "express";
import { z as z2 } from "zod";
var router4 = Router4();
var requireAdmin = async (req, res, next) => {
  try {
    const user = await storage.getUser(req.user.id);
    if (!user || !user.email?.includes("admin")) {
      return res.status(403).json({
        success: false,
        message: "Admin access required"
      });
    }
    next();
  } catch (error) {
    logger.error("Admin auth check failed", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Authentication check failed"
    });
  }
};
router4.use(authenticateToken);
router4.use(requireAdmin);
var createDomainSchema = z2.object({
  name: z2.string().min(1).max(255),
  description: z2.string().min(1).max(1e3),
  examPercentage: z2.number().min(1).max(100),
  color: z2.string().regex(/^#[0-9A-F]{6}$/i),
  icon: z2.string().min(1).max(50)
});
var createScenarioSchema = z2.object({
  title: z2.string().min(1).max(255),
  description: z2.string().min(1).max(1e3),
  type: z2.enum(["lab", "scenario", "challenge"]),
  domainId: z2.number().int().positive(),
  difficulty: z2.enum(["beginner", "intermediate", "advanced"]),
  estimatedTime: z2.number().int().positive(),
  xpReward: z2.number().int().positive(),
  content: z2.object({
    background: z2.string(),
    scenario: z2.string(),
    objectives: z2.array(z2.string()).optional(),
    questions: z2.array(
      z2.object({
        id: z2.number(),
        question: z2.string(),
        options: z2.array(z2.string()),
        correct: z2.number(),
        explanation: z2.string()
      })
    ).optional(),
    codeExample: z2.string().optional()
  })
});
var createAchievementSchema = z2.object({
  name: z2.string().min(1).max(255),
  description: z2.string().min(1).max(1e3),
  icon: z2.string().min(1).max(50),
  xpReward: z2.number().int().positive(),
  criteria: z2.record(z2.any())
});
router4.get("/dashboard", async (req, res) => {
  try {
    const users2 = await storage.getAllDomains();
    const domains2 = await storage.getAllDomains();
    const scenarios2 = await storage.getAllScenarios();
    const achievements2 = await storage.getAllAchievements();
    const stats = {
      totalUsers: 0,
      // Would need user count method
      totalDomains: domains2.length,
      totalScenarios: scenarios2.length,
      totalAchievements: achievements2.length,
      recentActivity: [],
      // Would need activity tracking
      popularScenarios: scenarios2.slice(0, 5),
      // Would need popularity metrics
      systemHealth: {
        database: "healthy",
        websocket: "healthy",
        memory: process.memoryUsage(),
        uptime: process.uptime()
      }
    };
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error("Error fetching admin dashboard", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch dashboard data"
    });
  }
});
router4.get("/domains", async (req, res) => {
  try {
    const domains2 = await storage.getAllDomains();
    res.json({
      success: true,
      data: { domains: domains2 }
    });
  } catch (error) {
    logger.error("Error fetching domains for admin", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch domains"
    });
  }
});
router4.post("/domains", validateBody(createDomainSchema), async (req, res) => {
  try {
    logger.info("Domain creation requested", { data: req.body, adminId: req.user?.id });
    res.status(501).json({
      success: false,
      message: "Domain creation not yet implemented"
    });
  } catch (error) {
    logger.error("Error creating domain", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to create domain"
    });
  }
});
router4.get("/scenarios", async (req, res) => {
  try {
    const scenarios2 = await storage.getAllScenarios();
    const domains2 = await storage.getAllDomains();
    const scenariosWithDomains = scenarios2.map((scenario) => {
      const domain = domains2.find((d) => d.id === scenario.domainId);
      return { ...scenario, domainName: domain?.name || "Unknown" };
    });
    res.json({
      success: true,
      data: { scenarios: scenariosWithDomains }
    });
  } catch (error) {
    logger.error("Error fetching scenarios for admin", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch scenarios"
    });
  }
});
router4.post("/scenarios", validateBody(createScenarioSchema), async (req, res) => {
  try {
    logger.info("Scenario creation requested", { data: req.body, adminId: req.user?.id });
    res.status(501).json({
      success: false,
      message: "Scenario creation not yet implemented"
    });
  } catch (error) {
    logger.error("Error creating scenario", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to create scenario"
    });
  }
});
router4.put(
  "/scenarios/:id",
  validateParams(scenarioIdParamSchema),
  validateBody(createScenarioSchema),
  async (req, res) => {
    try {
      const scenarioId = parseInt(req.params.id);
      logger.info("Scenario update requested", { scenarioId, data: req.body, adminId: req.user?.id });
      res.status(501).json({
        success: false,
        message: "Scenario update not yet implemented"
      });
    } catch (error) {
      logger.error("Error updating scenario", { error: error.message });
      res.status(500).json({
        success: false,
        message: "Failed to update scenario"
      });
    }
  }
);
router4.delete(
  "/scenarios/:id",
  validateParams(scenarioIdParamSchema),
  async (req, res) => {
    try {
      const scenarioId = parseInt(req.params.id);
      logger.info("Scenario deletion requested", { scenarioId, adminId: req.user?.id });
      res.status(501).json({
        success: false,
        message: "Scenario deletion not yet implemented"
      });
    } catch (error) {
      logger.error("Error deleting scenario", { error: error.message });
      res.status(500).json({
        success: false,
        message: "Failed to delete scenario"
      });
    }
  }
);
router4.get("/achievements", async (req, res) => {
  try {
    const achievements2 = await storage.getAllAchievements();
    res.json({
      success: true,
      data: { achievements: achievements2 }
    });
  } catch (error) {
    logger.error("Error fetching achievements for admin", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch achievements"
    });
  }
});
router4.post(
  "/achievements",
  validateBody(createAchievementSchema),
  async (req, res) => {
    try {
      logger.info("Achievement creation requested", { data: req.body, adminId: req.user?.id });
      res.status(501).json({
        success: false,
        message: "Achievement creation not yet implemented"
      });
    } catch (error) {
      logger.error("Error creating achievement", { error: error.message });
      res.status(500).json({
        success: false,
        message: "Failed to create achievement"
      });
    }
  }
);
router4.get("/users", async (req, res) => {
  try {
    logger.info("User list requested", { adminId: req.user?.id });
    res.status(501).json({
      success: false,
      message: "User management not yet implemented"
    });
  } catch (error) {
    logger.error("Error fetching users for admin", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to fetch users"
    });
  }
});
router4.get("/system/health", async (req, res) => {
  try {
    const health = {
      status: "healthy",
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
      environment: process.env.NODE_ENV || "development"
    };
    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    logger.error("Error checking system health", { error: error.message });
    res.status(500).json({
      success: false,
      message: "Failed to check system health"
    });
  }
});
var admin_default = router4;

// server/app.ts
var app = express();
setupGlobalErrorHandlers();
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://replit.com"],
        connectSrc: ["'self'", "ws:", "wss:"]
      }
    },
    crossOriginEmbedderPolicy: false
  })
);
var corsOptions = {
  origin: function(origin, callback) {
    if (!origin) return callback(null, true);
    const allowedOrigins = [
      "http://localhost:3000",
      "http://localhost:5000",
      "http://127.0.0.1:3000",
      "http://127.0.0.1:5000"
    ];
    if (process.env.ALLOWED_ORIGINS) {
      allowedOrigins.push(...process.env.ALLOWED_ORIGINS.split(","));
    }
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      logger.warn("CORS blocked request from origin", { origin });
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Origin", "X-Requested-With", "Content-Type", "Accept", "Authorization"]
};
app.use(cors(corsOptions));
var limiter = rateLimit({
  windowMs: 15 * 60 * 1e3,
  // 15 minutes
  max: 100,
  // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: "Too many requests from this IP, please try again later."
  },
  standardHeaders: true,
  legacyHeaders: false
});
var authLimiter = rateLimit({
  windowMs: 15 * 60 * 1e3,
  // 15 minutes
  max: 5,
  // limit each IP to 5 auth requests per windowMs
  message: {
    success: false,
    message: "Too many authentication attempts, please try again later."
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use("/api/", limiter);
app.use("/api/auth/", authLimiter);
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false, limit: "10mb" }));
app.use((req, res, next) => {
  const start = Date.now();
  res.on("finish", () => {
    const duration = Date.now() - start;
    logger.info("HTTP Request", {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get("User-Agent")
    });
  });
  next();
});
app.get("/health", (req, res) => {
  res.json({
    success: true,
    message: "Server is healthy",
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    uptime: process.uptime()
  });
});
app.use("/api/auth", auth_default);
app.use("/api/users", users_default);
app.use("/api/admin", admin_default);
app.use("/api", content_default);
app.get("/api/users/:id", authenticateToken, async (req, res) => {
  res.redirect(`/api/users/${req.params.id}`);
});
app.get("/api/domains", async (req, res) => {
  res.redirect("/api/domains");
});
app.get("/api/scenarios", async (req, res) => {
  res.redirect("/api/scenarios");
});
app.use("/api/*", notFoundHandler);
app.use(errorHandler);
var staticOptions = {
  setHeaders: (res, filePath) => {
    if (filePath.endsWith(".css")) {
      res.setHeader("Content-Type", "text/css");
    } else if (filePath.endsWith(".js")) {
      res.setHeader("Content-Type", "application/javascript");
    } else if (filePath.endsWith(".json")) {
      res.setHeader("Content-Type", "application/json");
    }
  },
  maxAge: process.env.NODE_ENV === "production" ? "1y" : "0"
};
if (process.env.NODE_ENV === "production") {
  const publicPath = path2.resolve(process.cwd(), "dist", "public");
  app.use(express.static(publicPath, staticOptions));
  app.get("*", (req, res) => {
    if (!req.path.startsWith("/api")) {
      res.sendFile(path2.resolve(publicPath, "index.html"));
    }
  });
} else {
  const distPath = path2.resolve(process.cwd(), "dist", "public");
  app.use(express.static(distPath, staticOptions));
  app.get("*", (req, res) => {
    if (!req.path.startsWith("/api")) {
      res.sendFile(path2.resolve(distPath, "index.html"));
    }
  });
}
var app_default = app;

// server/websocket-service.ts
import { WebSocketServer, WebSocket } from "ws";
var WebSocketService = class {
  wss;
  clients = /* @__PURE__ */ new Map();
  constructor(server2) {
    this.wss = new WebSocketServer({
      server: server2,
      path: "/ws",
      verifyClient: this.verifyClient.bind(this)
    });
    this.wss.on("connection", this.handleConnection.bind(this));
    this.setupHeartbeat();
    logger.info("WebSocket service initialized");
  }
  async verifyClient(info) {
    try {
      const url = new URL(info.req.url || "", `http://${info.req.headers.host}`);
      const token = url.searchParams.get("token");
      if (!token) {
        logger.warn("WebSocket connection rejected: No token provided");
        return false;
      }
      const payload = AuthService.verifyToken(token);
      if (!payload || payload.type !== "access") {
        logger.warn("WebSocket connection rejected: Invalid token");
        return false;
      }
      info.req.userId = payload.userId;
      return true;
    } catch (error) {
      logger.warn("WebSocket verification failed", { error: error.message });
      return false;
    }
  }
  handleConnection(ws, req) {
    const userId = req.userId;
    ws.userId = userId;
    ws.isAlive = true;
    if (!this.clients.has(userId)) {
      this.clients.set(userId, []);
    }
    this.clients.get(userId).push(ws);
    logger.info("WebSocket client connected", { userId, totalConnections: this.wss.clients.size });
    this.sendToClient(ws, {
      type: "connected",
      data: { message: "Connected to CyberDefense Simulator" },
      timestamp: Date.now()
    });
    ws.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMessage(ws, message);
      } catch (error) {
        logger.warn("Invalid WebSocket message", { error: error.message, userId });
      }
    });
    ws.on("close", () => {
      this.removeClient(userId, ws);
      logger.info("WebSocket client disconnected", { userId });
    });
    ws.on("pong", () => {
      ws.isAlive = true;
    });
    ws.on("error", (error) => {
      logger.error("WebSocket error", { error: error.message, userId });
      this.removeClient(userId, ws);
    });
  }
  handleMessage(ws, message) {
    const userId = ws.userId;
    switch (message.type) {
      case "ping":
        this.sendToClient(ws, {
          type: "pong",
          timestamp: Date.now()
        });
        break;
      case "subscribe":
        this.handleSubscription(ws, message.data);
        break;
      case "scenario_start":
        this.broadcastToUser(userId, {
          type: "scenario_started",
          data: { scenarioId: message.data.scenarioId },
          timestamp: Date.now()
        });
        break;
      case "scenario_progress":
        this.broadcastToUser(userId, {
          type: "scenario_progress_update",
          data: message.data,
          timestamp: Date.now()
        });
        break;
      default:
        logger.warn("Unknown WebSocket message type", { type: message.type, userId });
    }
  }
  handleSubscription(ws, subscriptionData) {
    ws.subscriptions = subscriptionData;
    this.sendToClient(ws, {
      type: "subscription_confirmed",
      data: subscriptionData,
      timestamp: Date.now()
    });
  }
  removeClient(userId, ws) {
    const userClients = this.clients.get(userId);
    if (userClients) {
      const index2 = userClients.indexOf(ws);
      if (index2 > -1) {
        userClients.splice(index2, 1);
      }
      if (userClients.length === 0) {
        this.clients.delete(userId);
      }
    }
  }
  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error("Failed to send WebSocket message", { error: error.message });
      }
    }
  }
  // Public methods for broadcasting events
  broadcastToUser(userId, message) {
    const userClients = this.clients.get(userId);
    if (userClients) {
      userClients.forEach((ws) => {
        this.sendToClient(ws, message);
      });
    }
  }
  broadcastToAll(message) {
    this.wss.clients.forEach((ws) => {
      this.sendToClient(ws, message);
    });
  }
  notifyAchievementEarned(userId, achievement) {
    this.broadcastToUser(userId, {
      type: "achievement_earned",
      data: achievement,
      timestamp: Date.now()
    });
  }
  notifyProgressUpdate(userId, progress) {
    this.broadcastToUser(userId, {
      type: "progress_updated",
      data: progress,
      timestamp: Date.now()
    });
  }
  notifyScenarioCompleted(userId, scenarioData) {
    this.broadcastToUser(userId, {
      type: "scenario_completed",
      data: scenarioData,
      timestamp: Date.now()
    });
  }
  notifyLeaderboardUpdate(leaderboardData) {
    this.broadcastToAll({
      type: "leaderboard_updated",
      data: leaderboardData,
      timestamp: Date.now()
    });
  }
  // Heartbeat to detect broken connections
  setupHeartbeat() {
    setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          ws.terminate();
          return;
        }
        ws.isAlive = false;
        ws.ping();
      });
    }, 3e4);
  }
  getConnectionStats() {
    return {
      totalConnections: this.wss.clients.size,
      uniqueUsers: this.clients.size,
      userConnections: Array.from(this.clients.entries()).map(([userId, connections]) => ({
        userId,
        connections: connections.length
      }))
    };
  }
  close() {
    this.wss.close();
    logger.info("WebSocket service closed");
  }
};

// server/index.ts
var server = createServer(app_default);
var port = process.env.PORT || 5e3;
var wsService;
process.on("SIGTERM", () => {
  logger.info("SIGTERM received, shutting down gracefully");
  server.close(async () => {
    if (wsService) wsService.close();
    await closeDatabaseConnection();
    logger.info("Process terminated");
    process.exit(0);
  });
});
process.on("SIGINT", () => {
  logger.info("SIGINT received, shutting down gracefully");
  server.close(async () => {
    if (wsService) wsService.close();
    await closeDatabaseConnection();
    logger.info("Process terminated");
    process.exit(0);
  });
});
async function startServer() {
  try {
    await initializeDatabase();
    server.listen(port, () => {
      wsService = new WebSocketService(server);
      logger.info(`CyberDefense Simulator server started`, {
        port,
        environment: process.env.NODE_ENV || "development",
        url: `http://localhost:${port}`,
        websocket: `ws://localhost:${port}/ws`,
        database: process.env.DATABASE_URL ? "Connected" : "In-memory"
      });
    });
  } catch (error) {
    logger.error("Failed to start server", { error: error instanceof Error ? error.message : String(error) });
    process.exit(1);
  }
}
startServer();
