#!/usr/bin/env node

/**
 * Simple CORS test script to verify the server configuration
 * Run this after starting the server to test CORS functionality
 */

const http = require('http');
const https = require('https');

const testOrigins = [
  'http://localhost:3000',
  'http://localhost:5000',
  'https://cyberdefensesim.onrender.com',
  'https://example.com' // This should be blocked
];

const serverUrl = process.env.TEST_SERVER_URL || 'http://localhost:5000';

async function testCORS(origin) {
  return new Promise((resolve) => {
    const url = new URL('/health', serverUrl);
    const client = url.protocol === 'https:' ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'GET',
      headers: {
        'Origin': origin,
        'User-Agent': 'CORS-Test-Script/1.0'
      }
    };

    const req = client.request(options, (res) => {
      const corsHeader = res.headers['access-control-allow-origin'];
      const allowed = corsHeader === origin || corsHeader === '*';
      
      resolve({
        origin,
        status: res.statusCode,
        corsHeader,
        allowed,
        success: res.statusCode === 200 && allowed
      });
    });

    req.on('error', (error) => {
      resolve({
        origin,
        error: error.message,
        success: false
      });
    });

    req.end();
  });
}

async function runTests() {
  console.log(`Testing CORS configuration for server: ${serverUrl}\n`);
  
  for (const origin of testOrigins) {
    try {
      const result = await testCORS(origin);
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      
      console.log(`${status} Origin: ${origin}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      } else {
        console.log(`   Status: ${result.status}`);
        console.log(`   CORS Header: ${result.corsHeader || 'None'}`);
        console.log(`   Allowed: ${result.allowed}`);
      }
      console.log('');
    } catch (error) {
      console.log(`❌ FAIL Origin: ${origin}`);
      console.log(`   Error: ${error.message}\n`);
    }
  }
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testCORS, runTests };
