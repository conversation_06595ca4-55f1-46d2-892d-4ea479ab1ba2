/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const CirclePower = createLucideIcon("CirclePower", [
  ["path", { d: "M12 7v4", key: "xawao1" }],
  ["path", { d: "M7.998 9.003a5 5 0 1 0 8-.005", key: "1pek45" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
]);

export { CirclePower as default };
//# sourceMappingURL=circle-power.js.map
