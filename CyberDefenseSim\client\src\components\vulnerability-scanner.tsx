import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { AlertTriangle, Shield, Zap, Search } from 'lucide-react';

interface VulnerabilityResult {
  id: string;
  severity: 'Critical' | 'High' | 'Medium' | 'Low';
  title: string;
  description: string;
  cvss: number;
  category: string;
}

export default function VulnerabilityScanner() {
  const [target, setTarget] = useState('');
  const [scanning, setScanning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<VulnerabilityResult[]>([]);

  const simulateVulnerabilityScan = async () => {
    setScanning(true);
    setProgress(0);
    setResults([]);

    // Simulate scanning progress
    for (let i = 0; i <= 100; i += 10) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Simulate scan results based on CompTIA Security+ scenarios
    const mockResults: VulnerabilityResult[] = [
      {
        id: 'CVE-2023-0001',
        severity: 'Critical',
        title: 'SQL Injection in Authentication',
        description: 'Application vulnerable to boolean-based blind SQL injection in login form',
        cvss: 9.8,
        category: 'Application Security',
      },
      {
        id: 'CVE-2023-0002',
        severity: 'High',
        title: 'Cross-Site Scripting (XSS)',
        description: 'Stored XSS vulnerability in message board allows script injection',
        cvss: 8.2,
        category: 'Web Application',
      },
      {
        id: 'CVE-2023-0003',
        severity: 'Medium',
        title: 'Directory Traversal',
        description: 'Path traversal vulnerability allows access to sensitive files',
        cvss: 6.5,
        category: 'Information Disclosure',
      },
      {
        id: 'CVE-2023-0004',
        severity: 'High',
        title: 'Session Fixation',
        description: 'Application does not regenerate session ID after authentication',
        cvss: 7.4,
        category: 'Session Management',
      },
      {
        id: 'CVE-2023-0005',
        severity: 'Low',
        title: 'Information Disclosure',
        description: 'Server version disclosed in HTTP headers',
        cvss: 3.1,
        category: 'Information Disclosure',
      },
    ];

    setResults(mockResults);
    setScanning(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical':
        return 'bg-red-600 text-white';
      case 'High':
        return 'bg-orange-500 text-white';
      case 'Medium':
        return 'bg-yellow-500 text-black';
      case 'Low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'Critical':
      case 'High':
        return <AlertTriangle className="w-4 h-4" />;
      case 'Medium':
        return <Zap className="w-4 h-4" />;
      case 'Low':
        return <Shield className="w-4 h-4" />;
      default:
        return <Shield className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="w-5 h-5" />
            <span>Vulnerability Scanner</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Input
              placeholder="Enter target IP address or domain (e.g., ***********)"
              value={target}
              onChange={e => setTarget(e.target.value)}
              className="flex-1"
            />
            <Button onClick={simulateVulnerabilityScan} disabled={scanning || !target}>
              {scanning ? 'Scanning...' : 'Start Scan'}
            </Button>
          </div>

          {scanning && (
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-2">
                <span>Scanning progress</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} />
            </div>
          )}
        </CardContent>
      </Card>

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Scan Results</CardTitle>
            <div className="text-sm text-muted-foreground">Found {results.length} vulnerabilities in target system</div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {results.map(result => (
                <div key={result.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Badge className={getSeverityColor(result.severity)}>
                        {getSeverityIcon(result.severity)}
                        <span className="ml-1">{result.severity}</span>
                      </Badge>
                      <span className="font-medium">{result.title}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">CVSS: {result.cvss}</div>
                  </div>
                  <p className="text-sm text-muted-foreground">{result.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{result.category}</Badge>
                    <span className="text-xs text-muted-foreground">{result.id}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
